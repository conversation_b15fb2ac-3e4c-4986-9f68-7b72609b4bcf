<template>  
  <!-- Asymmetrical Bento Grid -->
  <div class="grid grid-cols-1 md:grid-cols-6 lg:grid-cols-8 gap-6 max-w-7xl mx-auto">
    <!-- Content Sources (Large - spans 3 columns) -->
    <UCard class="md:col-span-3 lg:col-span-3 group hover:shadow-lg transition-all duration-300 border-2 border-dashed border-gray-300 dark:border-gray-600">
      <div class="p-6">
        <div class="flex items-center gap-3 mb-4">
          <UIcon name="i-ph-film-strip" class="w-8 h-8 text-blue-500" />
          <h3 class="text-xl font-600">Diverse Content Sources</h3>
        </div>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          From blockbuster films and acclaimed TV series to indie games and classic literature.
        </p>
        <div class="space-y-3">
          <div class="flex items-center gap-2 text-sm">
            <UIcon name="i-ph-star" class="w-4 h-4 text-yellow-500" />
            <span class="font-medium">Star Wars</span>
            <span class="text-gray-500">— Epic space saga</span>
          </div>
          <div class="flex items-center gap-2 text-sm">
            <UIcon name="i-ph-game-controller" class="w-4 h-4 text-purple-500" />
            <span class="font-medium">Clair-Obscure: Expedition 33</span>
            <span class="text-gray-500">— Gaming wisdom</span>
          </div>
          <div class="flex items-center gap-2 text-sm">
            <UIcon name="i-ph-book" class="w-4 h-4 text-green-500" />
            <span class="font-medium">Classic Literature</span>
            <span class="text-gray-500">— Timeless words</span>
          </div>
        </div>
      </div>
    </UCard>

    <!-- Community-Driven (Medium - spans 2 columns) -->
    <UCard class="md:col-span-2 lg:col-span-2 group hover:shadow-lg transition-all duration-300 border-2 border-dashed border-gray-300 dark:border-gray-600">
      <div class="p-6">
        <div class="flex items-center gap-3 mb-4">
          <UIcon name="i-ph-users" class="w-8 h-8 text-green-500" />
          <h3 class="text-lg font-600">Community-Driven</h3>
        </div>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          Built by quote enthusiasts, for quote enthusiasts. Every contribution matters.
        </p>
        <div class="flex items-center gap-2 text-sm text-green-600 dark:text-green-400">
          <UIcon name="i-ph-heart" class="w-4 h-4" />
          <span>User submissions welcome</span>
        </div>
      </div>
    </UCard>

    <!-- Search Functionality (Medium - spans 2 columns) -->
    <UCard class="md:col-span-2 lg:col-span-2 group hover:shadow-lg transition-all duration-300 border-2 border-dashed border-gray-300 dark:border-gray-600">
      <div class="p-6">
        <div class="flex items-center gap-3 mb-4">
          <UIcon name="i-ph-magnifying-glass" class="w-8 h-8 text-purple-500" />
          <h3 class="text-lg font-600">Powerful Search</h3>
        </div>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          Find the perfect quote with advanced search across authors, references, and content.
        </p>
        <div class="flex items-center gap-2 text-sm text-purple-600 dark:text-purple-400">
          <UIcon name="i-ph-lightning" class="w-4 h-4" />
          <span>Instant results</span>
        </div>
      </div>
    </UCard>

    <!-- Universal Access (Small - spans 1 column) -->
    <UCard class="md:col-span-1 lg:col-span-1 group hover:shadow-lg transition-all duration-300 border-2 border-dashed border-gray-300 dark:border-gray-600">
      <div class="p-4">
        <div class="text-center">
          <UIcon name="i-ph-globe" class="w-8 h-8 text-blue-500 mx-auto mb-3" />
          <h3 class="text-sm font-600 mb-2">Universal Access</h3>
          <p class="text-xs text-gray-600 dark:text-gray-400">
            Web-based, works everywhere
          </p>
        </div>
      </div>
    </UCard>

    <!-- Open Source (Small - spans 1 column) -->
    <UCard class="md:col-span-1 lg:col-span-1 group hover:shadow-lg transition-all duration-300 border-2 border-dashed border-gray-300 dark:border-gray-600">
      <div class="p-4">
        <div class="text-center">
          <UIcon name="i-simple-icons-github" class="w-8 h-8 text-gray-800 dark:text-gray-200 mx-auto mb-3" />
          <h3 class="text-sm font-600 mb-2">Open Source</h3>
          <p class="text-xs text-gray-600 dark:text-gray-400">
            Transparent & collaborative
          </p>
        </div>
      </div>
    </UCard>

    <!-- Moderation (Small - spans 1 column) -->
    <UCard class="md:col-span-1 lg:col-span-1 group hover:shadow-lg transition-all duration-300 border-2 border-dashed border-gray-300 dark:border-gray-600">
      <div class="p-4">
        <div class="text-center">
          <UIcon name="i-ph-shield-check" class="w-8 h-8 text-red-500 mx-auto mb-3" />
          <h3 class="text-sm font-600 mb-2">Quality Control</h3>
          <p class="text-xs text-gray-600 dark:text-gray-400">
            Curated content
          </p>
        </div>
      </div>
    </UCard>

    <!-- Collections (Medium - spans 2 columns) -->
    <UCard class="md:col-span-2 lg:col-span-2 group hover:shadow-lg transition-all duration-300 border-2 border-dashed border-gray-300 dark:border-gray-600">
      <div class="p-6">
        <div class="flex items-center gap-3 mb-4">
          <UIcon name="i-ph-bookmark" class="w-8 h-8 text-orange-500" />
          <h3 class="text-lg font-600">Personal Collections</h3>
        </div>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          Organize your favorite quotes into themed collections and share them with others.
        </p>
        <div class="flex items-center gap-2 text-sm text-orange-600 dark:text-orange-400">
          <UIcon name="i-ph-folder" class="w-4 h-4" />
          <span>Create & share collections</span>
        </div>
      </div>
    </UCard>
  </div>
</template>
