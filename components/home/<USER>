<template>
  <div class="px-8 font-text">
    <!-- Onboarding Section (Centered when database is empty) -->
    <div v-if="needsOnboarding" class="text-center py-16 mb-16">
      <div class="max-w-2xl mx-auto">
        <UIcon name="i-ph-rocket-launch" class="w-16 h-16 text-primary-500 mx-auto mb-6" />
        <h2 class="text-3xl font-600 line-height-none mb-4">Welcome to Verbatims!</h2>
        <p class="text-gray-600 dark:text-gray-400 text-lg mb-8">
          Get started by setting up your universal quotes service:
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <UButton
            v-if="onboardingStatus?.step === 'admin_user' || !onboardingStatus?.hasAdminUser"
            size="lg"
            icon
            label="i-ph-user-plus"
            to="/onboarding/admin"
            class="px-8 py-4"
          >
            1. Create Admin User
          </UButton>
          <UButton
            v-else-if="onboardingStatus?.step === 'database_data' || !onboardingStatus?.hasData"
            size="lg"
            icon
            label="i-ph-database"
            to="/onboarding/database"
            class="px-8 py-4"
          >
            2. Initialize Database
          </UButton>
        </div>
      </div>
    </div>

    <!-- Empty State (when onboarding is complete but no quotes) -->
    <div v-else-if="stats.quotes === 0" class="text-center py-16 mb-16">
      <div class="max-w-2xl mx-auto">
        <UIcon name="i-ph-quotes" class="w-16 h-16 text-primary-500 mx-auto mb-6" />
        <h2 class="text-3xl font-600 line-height-none mb-4">Ready to start collecting quotes!</h2>
        <p class="text-gray-600 dark:text-gray-400 text-lg mb-8">
          Your database is set up. Start by submitting your first quote and begin building your universal quotes collection.
        </p>
        <UButton
          @click="$emit('openSubmitModal')"
          size="lg"
          icon
          label="i-ph-plus"
          class="px-8 py-4"
        >
          Submit Your First Quote
        </UButton>
      </div>
    </div>

    <!-- Features Bento Grid -->
    <div class="mb-20">
      <HomeEmptyBentoGrid />
    </div>

    <!-- Personal Manifesto Section -->
    <div class="max-w-4xl mx-auto mb-16">
      <UCard class="border-2 border-dashed border-gray-300 dark:border-gray-600">
        <div class="p-8">
          <div class="text-center mb-8">
            <UIcon name="i-ph-heart" class="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h2 class="text-3xl font-600 line-height-none mb-4">The Journey Behind Verbatims</h2>
            <p class="text-gray-600 dark:text-gray-400 text-lg">
              This is my 8th attempt at building something meaningful. Here's why it matters.
            </p>
          </div>

          <div class="prose prose-gray dark:prose-invert max-w-none">
            <p class="text-lg leading-relaxed mb-6">
              The project started with <strong>'Citamour'</strong> on Windows Phone 7, taking quotes from Evene.fr.
              Then I rebuilt <strong>'Citations 365'</strong> on Windows 8. After, I rebranded as <strong>'Memorare'</strong>
              coded with Svelte in 2020. Moved to Flutter in 2021 for universal app (Android, iOS, web) and renamed
              <strong>'kwotes'</strong>. Finally, after working on other projects, I released only on iOS.
            </p>

            <p class="text-lg leading-relaxed mb-6">
              Now I understand two things:
            </p>

            <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mb-6">
              <div class="flex items-start gap-4 mb-4">
                <div class="flex-shrink-0 w-8 h-8 bg-primary-500 text-white rounded-full flex items-center justify-center font-600">
                  1
                </div>
                <div>
                  <p class="text-lg leading-relaxed">
                    <strong>I lost myself in overly complicated considerations.</strong> Let's build a simple quotes web app
                    with the easiest technologies. No complicated notification system or paywall for search features.
                    Just focused on delivering emotional quotes.
                  </p>
                </div>
              </div>

              <div class="flex items-start gap-4">
                <div class="flex-shrink-0 w-8 h-8 bg-primary-500 text-white rounded-full flex items-center justify-center font-600">
                  2
                </div>
                <div>
                  <p class="text-lg leading-relaxed">
                    <strong>I seem obsessed with this project.</strong> I cannot let it go until it's finished.
                    Plus I don't find a viable alternative even with hundreds of quotes apps. None cover the features I desire.
                    So here we are again.
                  </p>
                </div>
              </div>
            </div>

            <div class="text-center">
              <p class="text-xl font-600 text-primary-600 dark:text-primary-400 mb-4">
                Welcome to Verbatims — built with passion, designed for simplicity.
              </p>
              <div class="flex justify-center gap-4">
                <UButton
                  variant="outline"
                  icon
                  label="i-simple-icons-github"
                  to="https://github.com/verbatims/verbatims"
                  external
                >
                  View on GitHub
                </UButton>
                <UButton
                  v-if="stats.quotes === 0"
                  @click="$emit('openSubmitModal')"
                  icon
                  label="i-ph-plus"
                >
                  Start Contributing
                </UButton>
              </div>
            </div>
          </div>
        </div>
      </UCard>
    </div>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  needsOnboarding: {
    type: Boolean,
    default: false
  },
  onboardingStatus: {
    type: Object,
    default: () => ({})
  },
  stats: {
    type: Object,
    default: () => ({ quotes: 0, authors: 0, references: 0, users: 0 })
  }
})

// Emits
const emit = defineEmits(['openSubmitModal'])
</script>
